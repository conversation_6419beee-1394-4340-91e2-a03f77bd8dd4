// Simplified conversation models for ElevenLabs WebSocket

enum SimpleConversationStatus {
  disconnected,
  connecting,
  connected,
  speaking,
  listening,
  error,
}

// Base message class
abstract class SimpleConversationMessage {
  final String type;

  SimpleConversationMessage({required this.type});

  Map<String, dynamic> toJson();

  factory SimpleConversationMessage.fromJson(Map<String, dynamic> json) {
    final type = json['type'] as String;
    
    switch (type) {
      case 'conversation_initiation_client_data':
        return ConversationInitiation.fromJson(json);
      case 'audio':
        return AudioMessage.fromJson(json);
      case 'user_transcript':
        return UserTranscript.fromJson(json);
      case 'agent_response':
        return AgentResponse.fromJson(json);
      case 'ping':
        return PingMessage.fromJson(json);
      case 'pong':
        return PongMessage.fromJson(json);
      case 'conversation_initiation_metadata':
        // Ignore metadata messages for now
        return MetadataMessage();
      default:
        // Log unknown types but don't throw
        print('Warning: Unknown message type: $type');
        return UnknownMessage(type: type);
    }
  }
}

// Conversation initiation
class ConversationInitiation extends SimpleConversationMessage {
  final String conversationId;

  ConversationInitiation({required this.conversationId})
      : super(type: 'conversation_initiation_client_data');

  @override
  Map<String, dynamic> toJson() => {
        'type': type,
        'conversation_id': conversationId,
      };

  factory ConversationInitiation.fromJson(Map<String, dynamic> json) {
    return ConversationInitiation(
      conversationId: json['conversation_id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
    );
  }
}

// Audio message (for sending user audio)
class UserAudioChunk extends SimpleConversationMessage {
  final String audioBase64;

  UserAudioChunk({required this.audioBase64}) : super(type: 'user_audio_chunk');

  @override
  Map<String, dynamic> toJson() => {
        'user_audio_chunk': audioBase64,
      };
}

// Audio message (for receiving from server)
class AudioMessage extends SimpleConversationMessage {
  final String audioBase64;

  AudioMessage({required this.audioBase64}) : super(type: 'audio');

  @override
  Map<String, dynamic> toJson() => {
        'type': type,
        'audio_base64': audioBase64,
      };

  factory AudioMessage.fromJson(Map<String, dynamic> json) {
    // Handle both sending format and receiving format
    if (json['audio_event'] != null) {
      // Receiving format from server
      return AudioMessage(audioBase64: json['audio_event']['audio_base_64'] ?? '');
    } else {
      // Sending format
      return AudioMessage(audioBase64: json['audio_base64'] ?? '');
    }
  }
}

// User transcript
class UserTranscript extends SimpleConversationMessage {
  final String text;

  UserTranscript({required this.text}) : super(type: 'user_transcript');

  @override
  Map<String, dynamic> toJson() => {
        'type': type,
        'user_transcription_event': {
          'user_transcript': text,
        },
      };

  factory UserTranscript.fromJson(Map<String, dynamic> json) {
    final event = json['user_transcription_event'];
    return UserTranscript(text: event?['user_transcript'] ?? '');
  }
}

// Agent response
class AgentResponse extends SimpleConversationMessage {
  final String text;

  AgentResponse({required this.text}) : super(type: 'agent_response');

  @override
  Map<String, dynamic> toJson() => {
        'type': type,
        'agent_response_event': {
          'agent_response': text,
        },
      };

  factory AgentResponse.fromJson(Map<String, dynamic> json) {
    final event = json['agent_response_event'];
    return AgentResponse(text: event?['agent_response'] ?? '');
  }
}

// Ping/Pong for keeping connection alive
class PingMessage extends SimpleConversationMessage {
  final int? eventId;

  PingMessage({this.eventId}) : super(type: 'ping');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    if (eventId != null) 'ping_event': {'event_id': eventId}
  };
  
  factory PingMessage.fromJson(Map<String, dynamic> json) {
    int? eventId;
    if (json['ping_event'] != null) {
      eventId = json['ping_event']['event_id'];
    }
    return PingMessage(eventId: eventId);
  }
}

class PongMessage extends SimpleConversationMessage {
  final int eventId;

  PongMessage({required this.eventId}) : super(type: 'pong');

  @override
  Map<String, dynamic> toJson() => {
    'type': type,
    'event_id': eventId,
  };

  factory PongMessage.fromJson(Map<String, dynamic> json) {
    return PongMessage(eventId: json['event_id'] ?? DateTime.now().millisecondsSinceEpoch);
  }
}

// Metadata message (ignored)
class MetadataMessage extends SimpleConversationMessage {
  MetadataMessage() : super(type: 'conversation_initiation_metadata');

  @override
  Map<String, dynamic> toJson() => {'type': type};
}

// Unknown message type
class UnknownMessage extends SimpleConversationMessage {
  UnknownMessage({required String type}) : super(type: type);

  @override
  Map<String, dynamic> toJson() => {'type': type};
}