# ElevenLabs Conversational AI - Implementation Guide

This document provides comprehensive instructions for the ElevenLabs Conversational AI integration in your fitness app, featuring <PERSON> as your AI fitness coach.

## 🎯 Prerequisites

1. **ElevenLabs Account**: Sign up at [elevenlabs.io](https://elevenlabs.io)
2. **Agent Created**: Your fitness coach agent "<PERSON>" is configured with ID: `agent_01jx195padeb0spkjn54zt3ah0`
3. **API Key**: You need to obtain your ElevenLabs API key

## 🔑 Getting Your API Key

1. Go to [ElevenLabs API Settings](https://elevenlabs.io/app/settings/api-keys)
2. Sign in to your account
3. Click "Create API Key" or copy an existing one
4. Copy the API key (it starts with something like `sk-...`)

## ⚙️ Configuration

1. **Update Environment File**:
   - Open the `.env` file in your project root
   - Replace `your_api_key_here` with your actual ElevenLabs API key:
   ```
   ELEVENLABS_API_KEY=sk-your-actual-api-key-here
   ```

2. **Agent Configuration** (Already Done):
   - Agent ID: `agent_01jx195padeb0spkjn54zt3ah0`
   - Agent Name: Nathan - Fitness Coach
   - Configured for fitness coaching context

## 🚀 Current Implementation Features

### Bi-directional Voice Conversation
- **Seamless Flow**: Start speaking naturally, Nathan responds, conversation continues automatically
- **Live Audio Streaming**: Real-time audio processing with no file intermediaries
- **Echo Cancellation**: Advanced echo prevention to avoid feedback loops
- **Stable WebSocket Connection**: Robust connection with proper ping/pong handling
- **Natural Interruption**: Conversation flows like talking to a real person

### Advanced Audio Processing
- **Live Streaming**: Audio streams directly to ElevenLabs without temporary files
- **Enhanced Recording**: Echo cancellation, auto-gain, and noise suppression enabled
- **Smart Pause/Resume**: Recording automatically pauses during Nathan's responses
- **High Quality**: 16kHz PCM audio with mono channel optimization

### Status Indicators
- **🔴 Red**: Listening for your voice (speak now)
- **🟢 Green**: Nathan is speaking (audio playing)
- **🔵 Blue**: Processing your message
- **🟠 Orange**: Ready for conversation
- **⚪ Grey**: Not connected or stopped

## 📱 How to Use

### Starting a Conversation
1. Open the "Voice" tab in your fitness app
2. Tap the **"Start Conversation"** button
3. Grant microphone permission if prompted
4. Wait for the connection to establish (2-3 seconds)
5. Start speaking naturally when you see the red listening indicator

### During Conversation
- **Speak naturally**: Just talk as you would to a personal trainer
- **Wait for responses**: Nathan will respond with voice and you'll see text transcription
- **Continue seamlessly**: No buttons to press - conversation flows automatically
- **Real-time feedback**: See conversation transcript as it happens

### Natural Flow Example
1. You: "Hi Nathan, I want to start a workout routine"
2. Nathan responds with voice + text appears in chat
3. Recording automatically resumes when Nathan finishes speaking
4. You continue: "I'm looking to build muscle"
5. Conversation continues naturally...

### Stopping
- Tap **"Stop Conversation"** to end the session
- All resources are cleaned up automatically

## 🔧 Technical Implementation

### WebSocket Architecture
- **Official API**: Uses ElevenLabs Conversational AI WebSocket API
- **Proper Message Formats**: Follows official ElevenLabs API documentation exactly
- **Connection Management**: Robust ping/pong handling and reconnection logic
- **Message Types**: UserAudioChunk, PongMessage, SessionUpdate properly formatted

### Audio System
- **Live Streaming**: `AudioRecorder.startStream()` with `StreamSubscription<Uint8List>`
- **Echo Prevention**: 
  - Enhanced recording config (`echoCancel: true`, `autoGain: true`, `noiseSuppress: true`)
  - Automatic pause/resume during Nathan's responses
  - Delay before resuming recording to prevent echo
- **Session Management**: Proper iOS/Android audio session handling

### Message Handling
```dart
// User audio chunks
{
  "user_audio_chunk": {
    "chunk": "base64_encoded_audio_data"
  }
}

// Pong responses (fixed format)
{
  "type": "pong",
  "event_id": 12345
}
```

## 🛠️ Recent Technical Improvements

### WebSocket Stability ✅
1. **Correct Pong Format**: Fixed pong message structure per official API docs
2. **Message Validation**: Proper JSON serialization for all message types
3. **Connection Lifecycle**: Enhanced connection management with keep-alive
4. **Error Handling**: Graceful handling of connection drops and reconnections

### Echo Cancellation ✅
1. **Advanced Audio Config**: Echo cancellation, auto-gain, noise suppression
2. **Smart Recording Control**: Pause during ALL audio playback
3. **Timing Optimization**: Delays to prevent recording agent's own voice
4. **Session Management**: Proper iOS/Android audio session control

### Live Audio Streaming ✅
1. **Real-time Processing**: Direct audio stream to WebSocket
2. **Memory Efficiency**: No temporary files or audio caching
3. **Pause/Resume Logic**: Seamless recording control during conversation
4. **Buffer Management**: Optimal audio chunk size and timing

### API Compliance ✅
1. **Official Format**: All messages follow ElevenLabs API specification exactly
2. **Removed Custom Logic**: No interfering ping timers or message wrappers
3. **Standard Flow**: Uses official conversation initiation and management
4. **Documentation Aligned**: Implementation matches official ElevenLabs docs

## 🐛 Troubleshooting

### Common Issues

1. **"Failed to start conversation" Error**:
   - Check your API key in the `.env` file
   - Ensure you have an active ElevenLabs subscription
   - Verify internet connection
   - Check agent ID is correct

2. **"Microphone permission required" Error**:
   - Tap "Open Settings" in the permission dialog
   - Find "Fitness App" and enable Microphone
   - Return to app and try again

3. **Echo or feedback loops**:
   - Use headphones if possible
   - Check device volume isn't too high
   - Ensure no other audio apps are running
   - The app has echo cancellation built-in

4. **WebSocket connection issues**:
   - Check internet connectivity is stable
   - Wait for full connection before speaking
   - Try restarting the conversation if connection drops

5. **Audio not playing**:
   - Check device volume and mute settings
   - Ensure no other apps are using audio
   - Try using headphones
   - Check notification/ringer volume on iOS

## 📊 Connection Health Indicators

### Healthy Connection
- ✅ WebSocket connects within 2-3 seconds
- ✅ Status shows "Connected! Starting conversation..."
- ✅ Audio plays clearly without echo
- ✅ Conversation transcript appears in real-time
- ✅ No connection drops during normal use

### Warning Signs
- ⚠️ Frequent "Connection lost, reconnecting..." messages
- ⚠️ Echo or hearing Nathan's voice in transcripts
- ⚠️ Long delays between speaking and Nathan's response
- ⚠️ Audio cutting out or robotic sounds

## 🎉 Success Indicators

When working correctly, you should experience:
- ✅ Instant connection establishment
- ✅ Natural conversation flow without manual controls
- ✅ Clear audio with no echo or feedback
- ✅ Real-time transcript updates
- ✅ Nathan responds contextually to fitness questions
- ✅ Stable connection throughout entire workout sessions
- ✅ Seamless start and stop functionality

## 🏋️ Fitness Integration Ready

The current implementation provides:
- **Hands-free operation** perfect for workouts
- **Real-time coaching** during exercises
- **Natural conversation** about fitness goals
- **Stable performance** throughout workout sessions
- **Echo-free experience** even in gym environments

## 🔗 Technical References

- [ElevenLabs Conversational AI API](https://elevenlabs.io/docs/conversational-ai/overview)
- [WebSocket Message Formats](https://elevenlabs.io/docs/conversational-ai/websocket)
- Implementation files:
  - `lib/shared/services/simple_elevenlabs_service.dart`
  - `lib/shared/models/simple_conversation_models.dart`
  - `lib/features/chat/presentation/screens/simple_voice_chat_screen.dart`

Start your conversation with Nathan and experience seamless AI fitness coaching! 🚀💪
